function requestPPSAvailabilities(){
  var site = "FC6"
  var year = 2025
  var week = 31

  Logger.log("Fetching availabilities for week... ")
  var url = "https://people-availability-proxy-prod.de.picnicinternational.com/availability/final?site_id=" + site + "&year=" + year + "&week=" + week 
  var auth_code = requestAuth()
  var headers = {"Authorization": "Basic " + auth_code}
  var response = UrlFetchApp.fetch(url, {"headers": headers, "method": "GET"})
  var data = JSON.parse(response)
  
  var exploded_availabilities = explodeDatesAvailabilities(data)
  var target_sheet = SpreadsheetApp.getActive().getSheetByName("DE PPS Availabilities");
  target_sheet.getRange("A2:J").clearContent()
  target_sheet.getRange(2,1,exploded_availabilities.length,exploded_availabilities[0].length).setValues(exploded_availabilities)
}

function explodeDatesAvailabilities(data) {
  var availabilities_exploded = []

  // Go through all PNs
  for (i = 0; i < data.length; i++) {
    var worker_record = data[i]
    var worker_id = worker_record["worker_id"]
    var site = worker_record["site"]
    var year_week = worker_record["year_week"]
    var requested_shifts = worker_record["requested_shifts"]
    var valid = worker_record["valid"]
    var source = worker_record["source"]
    var submitted_at = worker_record["submitted_at"]
    var created_at = worker_record["created_at"]

    // Go through all availabilities per PN
    var availabilities = worker_record["availabilities"]
    if (availabilities && availabilities.length > 0) {
      for (j = 0; j < availabilities.length; j++) {
        var availability = availabilities[j]
        var availability_id = availability["id"]
        var template_id = availability["template_id"]
        var start_time = availability["start_time"]
        var shift_name = availability["shift_name"]
        var duration = availability["duration"]
        var availability_valid = availability["valid"]

        // Convert start_time to readable date and time
        var formatted_date = ""
        var formatted_time = ""
        try {
          var start_date = new Date(start_time)
          formatted_date = Utilities.formatDate(start_date, "GMT+2", "dd/MM/yyyy")
          formatted_time = Utilities.formatDate(start_date, "GMT+2", "HH:mm")
        } catch(e) {
          formatted_date = start_time
          formatted_time = ""
        }

        availabilities_exploded.push([
          worker_id,
          site,
          year_week,
          requested_shifts,
          valid,
          source,
          submitted_at,
          created_at,
          availability_id,
          template_id,
          formatted_date,
          formatted_time,
          shift_name,
          duration,
          availability_valid
        ])
      }
    } else {
      // Falls keine Verfügbarkeiten vorhanden sind, trotzdem eine Zeile für den Mitarbeiter hinzufügen
      availabilities_exploded.push([
        worker_id,
        site,
        year_week,
        requested_shifts,
        valid,
        source,
        submitted_at,
        created_at,
        "", // availability_id
        "", // template_id
        "", // formatted_date
        "", // formatted_time
        "", // shift_name
        "", // duration
        ""  // availability_valid
      ])
    }
  }
  return availabilities_exploded
}