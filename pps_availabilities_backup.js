function requestPPSAvailabilities(){
  var site = "FC6"
  var year = 2025
  var week = 31

  Logger.log("Fetching availbilities for week... ")
  var url = "https://people-availability-proxy-prod.de.picnicinternational.com/availability/final?site_id=" + site + "&year=" + year + "&week=" + week 
  var auth_code = requestAuth()
  var headers = {"Authorization": "Basic " + auth_code}
  var response = UrlFetchApp.fetch(url, {"headers": headers, "method": "GET"})
  var data = JSON.parse(response)
  
  var exploded_availabilities = explodeDatesAvailabilities(data)
  var target_sheet = SpreadsheetApp.getActive().getSheetByName("DE PPS Availabilities");
  target_sheet.getRange("A2:J").clearContent()
  target_sheet.getRange(2,1,exploded_availabilities.length,exploded_availabilities[0].length).setValues(exploded_availabilities)
}

function explodeDatesAvailabilities(availabilities) {
  var availabilities_exploded = []
  for (i = 0; i < availabilities.length; i++) {
    var to_id       = availabilities[i]["time_off_id"]
    var user_id     = availabilities[i]["worker_id"]
    var type        = availabilities[i]["notes"]
    var hub         = availabilities[i]["site_id"]
    var date_start  = new Date(availabilities[i]["start"])
    var date_end    = new Date(availabilities[i]["end"])
    var date_iterator = date_start
    while (date_iterator < date_end) {
      try{
        var dayOfWeek = date_iterator.getDay()
        var isoWeek = Utilities.formatDate(subDaysFromDatetime(date_iterator,1), "GMT+2", "w")
        if (dayOfWeek === 0) {
          dayOfWeek = 7; // Sunday is 0, so set it to 7 for consistency
        }
      }
      catch{
        var dayOfWeek = ""
        var isoWeek = ""
      }
      availabilities_exploded.push([
       to_id,
       Utilities.formatDate(date_iterator, "GMT+2", "dd/MM/yyyy"),
       type,  
       hub,
       user_id,
       isoWeek,
       dayOfWeek])
      date_iterator = addDaysToDatetime(date_iterator, 1)
    }
  }
  return availabilities_exploded
}